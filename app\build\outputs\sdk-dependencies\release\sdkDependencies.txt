# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.13.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "22.4.0"
  }
  digests {
    sha256: "\256\361,\0355\271$\"q>E\366\360P\036\303\024\372\221t\301\224\250}\227\361\343\336\200\222\370\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "22.4.0"
  }
  digests {
    sha256: "\207\312\312\260e\236\221\362\316C[I\232\006\f\375\n\200t\350\302Z\212z~\237\242\276>G\aN"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.22"
  }
  digests {
    sha256: "\003\245\303\226\\\303pQ\022\216d\344gH\343\224\266\275L\227\372\201\306\336o\307+\375D\343B\033"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.22"
  }
  digests {
    sha256: "\320\3026^$7\357p\363E\206\325\017\005WC\367\227\026\274\376e\344\274r9\315\322f\236\367\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.5.0"
  }
  digests {
    sha256: "\243\337n\373)\276\262\377x\373|\336q*\307s\275l\334\337\311\203<\220\030\225\200O`\234\231^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.4"
  }
  digests {
    sha256: "\274<$1\335\244.\224\273\225\021\305\207\352\350\220\322v\344\252\3769:\215\247\260\001i\030m\257\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.2"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "22.4.0"
  }
  digests {
    sha256: "\245@4%\257\333\231\271\267\ry\020\336~\211l\235QH\001b\207m\246\227S\227\376y\331\203\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "22.4.0"
  }
  digests {
    sha256: "\327\000*\357A\362E\351}\'f\303\246m\312\031\352\vL\375\217$l;\212~\t\300\r\322|K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "T\nn-\307\2167\006\025\367\177\207,\017;p\n\032\312\035x\244\251\257}(\b\005\bH\306c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "\262\210]\265\335\255\233E\177\224\322\020A\332S&}\250\232\337\3448$+\240}\321\034\217\246>t"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "22.4.0"
  }
  digests {
    sha256: "\276\004\002 $\336?\273/\231\227\033\364\2144\341\324\r\226S\311T\3567\301\361@s\320\205\247\316"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "22.4.0"
  }
  digests {
    sha256: "}\337\a\f\351FP\0166;\304\rV6\355\206P\256\365\232\003\251\3768|<\356\375\265\271\373\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "22.4.0"
  }
  digests {
    sha256: "\313\202\273_v\tQ\177\352\002 \3263\220\006\375]p\317\300\260\224\256\225\003\340\234\214{\002\331\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.1"
  }
  digests {
    sha256: "K\301\327\263\205\307\r\374\006\330c\302\321\020|<\206cS\247\030\020\364\036D\227\250\355\335\231\027\216"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.2.1"
  }
  digests {
    sha256: "0\370\327\233x-(:\220\360\266\3676\221i\331\317V\000\221S\177\335`V\321\332\251\363\0037c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.1"
  }
  digests {
    sha256: "< T2\203(\203\036\266\346\233@\024\366\357\237\252\021\177\324\270\021\222\237:\221\323\3337_,\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "22.1.0"
  }
  digests {
    sha256: "\260\254\002p\t\262N`\026k\t\303\220\032y=zm\250\265=<\216V\037\034\2176 \352\271|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.4.0"
  }
  digests {
    sha256: "\341\220o\204Q/\036\245\344\311&\333\271\025x\232\330\367IO\244\356\232\322E\026?v\030\\\354\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-base"
    version: "22.1.0"
  }
  digests {
    sha256: "a\250f\244=x3\367\253Pr\201\271J<\246\371\n\035\023+\016y\026N\305KvI\303\372Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-lite"
    version: "22.1.0"
  }
  digests {
    sha256: "\301\300\205\0327`a\360\302)\241\257bhR$\360\375\210\200S.[\023I\377\'\a?u\312\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\210\202>\000\272\232\256m\306Cj\355Y(\300p\326\212b\302X\035\351ne\\\250o\003\016r\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.2.5"
  }
  digests {
    sha256: "$\245T\233ynC\3437Q=)\b\255\254g\364SP\331\251\v\312~.a i!@\273\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.2.5"
  }
  digests {
    sha256: "+\023\r\324\241\323\331\033g\001\3553\tm8\237\001\304\374\021\227\247\254\326\271\027$\335\305\254\374\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.1.0"
  }
  digests {
    sha256: "\206ss\177\333.\373\255\221\256\256\355\031\'\353\262\222\022\323j\206}\223\271c\234\200i\001\237\212\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.1.0"
  }
  digests {
    sha256: "\203A\377\t-``\326*\a\"\177)#qU\377\363o\261o\226\311_\275\232\210N7]\271\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "3.2.0"
  }
  digests {
    sha256: "\027\262\303\364Th\313\2057\246d\353x8\\\006\221\027\212\2534\020,\246\350U\220(\037!&&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.1"
  }
  digests {
    sha256: "\340N\315Ou\317E\312,7\273S\373 \224Z\234Z2,\025\341l$\031pmg/\260\020\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.3.6"
  }
  digests {
    sha256: "?\204\240\023\375\353\213\254\222\324\253`z\353\363\232O\371E\364XZcY`\355v\234\320%]\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.onesignal"
    artifactId: "OneSignal"
    version: "4.8.6"
  }
  digests {
    sha256: "e\r\235\337(\2174[\2146\266\245\236\362\262\200\a\n\324!\322\351\364\245\234\234\221\272\t\021ys"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "app-update"
    version: "2.1.0"
  }
  digests {
    sha256: "\025e\351\357\001O|\267\031?\004b\344\330\027H\n\377K3?\307\367\340\005+\2712\314(\202\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.4"
  }
  digests {
    sha256: "6@\b$\001O\306\035\223y\261=\321\253\023\366QHG\373\0044|\017\337\346\030\272\316x\363\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "review"
    version: "2.0.2"
  }
  digests {
    sha256: "\026\002\002fx#\035\272\017t\202\321$NE\377\203\331\377\"\374\213mk\350\226\022(\254z2\335"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 81
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 85
  library_dep_index: 77
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 71
  library_dep_index: 80
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 57
  library_dep_index: 28
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 70
  library_dep_index: 63
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 10
  library_dep_index: 3
  library_dep_index: 3
}
library_dependencies {
  library_index: 11
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 54
  library_dep_index: 47
  library_dep_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 12
  library_dep_index: 5
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 37
  library_dep_index: 52
  library_dep_index: 7
  library_dep_index: 53
  library_dep_index: 42
  library_dep_index: 7
}
library_dependencies {
  library_index: 13
  library_dep_index: 7
}
library_dependencies {
  library_index: 14
  library_dep_index: 5
  library_dep_index: 15
}
library_dependencies {
  library_index: 16
  library_dep_index: 7
  library_dep_index: 7
}
library_dependencies {
  library_index: 17
  library_dep_index: 5
}
library_dependencies {
  library_index: 18
  library_dep_index: 5
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 45
  library_dep_index: 7
  library_dep_index: 21
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 34
  library_dep_index: 40
  library_dep_index: 32
  library_dep_index: 39
}
library_dependencies {
  library_index: 19
  library_dep_index: 5
}
library_dependencies {
  library_index: 20
  library_dep_index: 5
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 22
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 50
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 9
  library_dep_index: 25
  library_dep_index: 8
  library_dep_index: 50
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 50
}
library_dependencies {
  library_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 30
  library_dep_index: 5
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 42
  library_dep_index: 33
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 47
  library_dep_index: 43
  library_dep_index: 48
  library_dep_index: 7
}
library_dependencies {
  library_index: 30
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 18
  library_dep_index: 31
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 43
  library_dep_index: 37
  library_dep_index: 7
  library_dep_index: 46
}
library_dependencies {
  library_index: 31
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 32
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 33
  library_dep_index: 7
  library_dep_index: 21
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 33
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 7
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 34
  library_dep_index: 33
  library_dep_index: 7
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 35
  library_dep_index: 5
  library_dep_index: 18
  library_dep_index: 36
  library_dep_index: 7
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 36
  library_dep_index: 5
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 5
}
library_dependencies {
  library_index: 38
  library_dep_index: 5
  library_dep_index: 18
  library_dep_index: 7
  library_dep_index: 22
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 39
  library_dep_index: 18
  library_dep_index: 7
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 40
  library_dep_index: 31
  library_dep_index: 7
  library_dep_index: 22
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 5
  library_dep_index: 42
  library_dep_index: 33
  library_dep_index: 31
  library_dep_index: 43
  library_dep_index: 7
  library_dep_index: 22
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 40
}
library_dependencies {
  library_index: 42
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 7
  library_dep_index: 12
  library_dep_index: 7
}
library_dependencies {
  library_index: 43
  library_dep_index: 5
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 7
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 43
  library_dep_index: 7
  library_dep_index: 43
}
library_dependencies {
  library_index: 45
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 36
  library_dep_index: 15
}
library_dependencies {
  library_index: 46
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 7
  library_dep_index: 30
}
library_dependencies {
  library_index: 47
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 48
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 3
}
library_dependencies {
  library_index: 50
  library_dep_index: 7
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 7
}
library_dependencies {
  library_index: 52
  library_dep_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 54
  library_dep_index: 5
}
library_dependencies {
  library_index: 55
  library_dep_index: 5
}
library_dependencies {
  library_index: 56
  library_dep_index: 5
}
library_dependencies {
  library_index: 57
  library_dep_index: 28
}
library_dependencies {
  library_index: 58
  library_dep_index: 28
}
library_dependencies {
  library_index: 59
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 60
  library_dep_index: 61
  library_dep_index: 57
  library_dep_index: 62
  library_dep_index: 28
  library_dep_index: 58
  library_dep_index: 63
  library_dep_index: 27
  library_dep_index: 64
}
library_dependencies {
  library_index: 60
  library_dep_index: 5
  library_dep_index: 42
  library_dep_index: 7
  library_dep_index: 23
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 42
  library_dep_index: 60
  library_dep_index: 15
  library_dep_index: 7
  library_dep_index: 23
  library_dep_index: 60
}
library_dependencies {
  library_index: 62
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 63
  library_dep_index: 11
  library_dep_index: 28
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
  library_dep_index: 15
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 70
  library_dep_index: 28
  library_dep_index: 58
}
library_dependencies {
  library_index: 71
  library_dep_index: 57
  library_dep_index: 28
  library_dep_index: 58
  library_dep_index: 70
  library_dep_index: 27
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 64
  library_dep_index: 7
}
library_dependencies {
  library_index: 72
  library_dep_index: 26
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 7
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
  library_dep_index: 5
  library_dep_index: 68
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 76
  library_dep_index: 72
  library_dep_index: 50
  library_dep_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 77
  library_dep_index: 27
  library_dep_index: 74
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 78
  library_dep_index: 7
}
library_dependencies {
  library_index: 78
  library_dep_index: 27
  library_dep_index: 74
}
library_dependencies {
  library_index: 79
  library_dep_index: 28
  library_dep_index: 74
}
library_dependencies {
  library_index: 80
  library_dep_index: 3
  library_dep_index: 28
  library_dep_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 81
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 82
  library_dep_index: 85
  library_dep_index: 88
  library_dep_index: 86
  library_dep_index: 89
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 5
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 84
  library_dep_index: 62
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 90
  library_dep_index: 63
  library_dep_index: 68
  library_dep_index: 7
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 87
  library_dep_index: 5
}
library_dependencies {
  library_index: 83
  library_dep_index: 5
}
library_dependencies {
  library_index: 84
  library_dep_index: 83
  library_dep_index: 5
  library_dep_index: 75
  library_dep_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 85
  library_dep_index: 5
}
library_dependencies {
  library_index: 86
  library_dep_index: 5
  library_dep_index: 85
}
library_dependencies {
  library_index: 87
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 85
  library_dep_index: 88
  library_dep_index: 5
}
library_dependencies {
  library_index: 88
  library_dep_index: 5
  library_dep_index: 85
}
library_dependencies {
  library_index: 89
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 90
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 91
  library_dep_index: 30
  library_dep_index: 5
  library_dep_index: 92
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 42
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 31
  library_dep_index: 45
  library_dep_index: 99
  library_dep_index: 43
  library_dep_index: 7
  library_dep_index: 92
}
library_dependencies {
  library_index: 92
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 91
}
library_dependencies {
  library_index: 93
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 3
}
library_dependencies {
  library_index: 94
  library_dep_index: 93
  library_dep_index: 17
  library_dep_index: 3
}
library_dependencies {
  library_index: 95
  library_dep_index: 5
}
library_dependencies {
  library_index: 96
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 49
}
library_dependencies {
  library_index: 97
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 98
}
library_dependencies {
  library_index: 98
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 97
  library_dep_index: 97
}
library_dependencies {
  library_index: 99
  library_dep_index: 5
}
library_dependencies {
  library_index: 100
  library_dep_index: 101
  library_dep_index: 68
  library_dep_index: 30
  library_dep_index: 5
  library_dep_index: 91
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 12
  library_dep_index: 96
  library_dep_index: 106
  library_dep_index: 13
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 107
  library_dep_index: 99
  library_dep_index: 108
  library_dep_index: 93
  library_dep_index: 109
}
library_dependencies {
  library_index: 101
  library_dep_index: 7
  library_dep_index: 51
  library_dep_index: 8
  library_dep_index: 50
}
library_dependencies {
  library_index: 102
  library_dep_index: 5
}
library_dependencies {
  library_index: 103
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 49
  library_dep_index: 3
}
library_dependencies {
  library_index: 104
  library_dep_index: 91
  library_dep_index: 105
  library_dep_index: 12
  library_dep_index: 45
}
library_dependencies {
  library_index: 105
  library_dep_index: 5
}
library_dependencies {
  library_index: 106
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 11
}
library_dependencies {
  library_index: 107
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 49
  library_dep_index: 3
}
library_dependencies {
  library_index: 108
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 106
}
library_dependencies {
  library_index: 109
  library_dep_index: 5
  library_dep_index: 29
  library_dep_index: 107
  library_dep_index: 12
  library_dep_index: 3
}
library_dependencies {
  library_index: 110
  library_dep_index: 111
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 112
  library_dep_index: 57
  library_dep_index: 113
  library_dep_index: 120
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 111
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 17
  library_dep_index: 12
  library_dep_index: 5
  library_dep_index: 15
}
library_dependencies {
  library_index: 112
  library_dep_index: 28
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
  library_dep_index: 112
  library_dep_index: 28
  library_dep_index: 70
  library_dep_index: 119
}
library_dependencies {
  library_index: 114
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 12
  library_dep_index: 115
  library_dep_index: 118
  library_dep_index: 117
  library_dep_index: 12
  library_dep_index: 39
}
library_dependencies {
  library_index: 115
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 20
}
library_dependencies {
  library_index: 116
  library_dep_index: 5
}
library_dependencies {
  library_index: 117
  library_dep_index: 5
  library_dep_index: 118
}
library_dependencies {
  library_index: 118
  library_dep_index: 5
}
library_dependencies {
  library_index: 119
  library_dep_index: 5
  library_dep_index: 28
}
library_dependencies {
  library_index: 120
  library_dep_index: 62
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 121
  library_dep_index: 5
  library_dep_index: 91
  library_dep_index: 12
  library_dep_index: 46
  library_dep_index: 122
  library_dep_index: 107
  library_dep_index: 123
  library_dep_index: 3
}
library_dependencies {
  library_index: 122
  library_dep_index: 29
  library_dep_index: 46
  library_dep_index: 42
  library_dep_index: 10
  library_dep_index: 34
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 7
}
library_dependencies {
  library_index: 123
  library_dep_index: 5
  library_dep_index: 49
  library_dep_index: 12
  library_dep_index: 124
  library_dep_index: 108
}
library_dependencies {
  library_index: 124
  library_dep_index: 7
  library_dep_index: 22
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 12
}
library_dependencies {
  library_index: 125
  library_dep_index: 81
  library_dep_index: 102
  library_dep_index: 126
  library_dep_index: 111
  library_dep_index: 91
  library_dep_index: 114
  library_dep_index: 51
  library_dep_index: 62
}
library_dependencies {
  library_index: 126
  library_dep_index: 12
  library_dep_index: 127
  library_dep_index: 11
  library_dep_index: 128
  library_dep_index: 29
}
library_dependencies {
  library_index: 127
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 52
}
library_dependencies {
  library_index: 128
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 49
  library_dep_index: 48
  library_dep_index: 103
  library_dep_index: 96
  library_dep_index: 123
  library_dep_index: 17
  library_dep_index: 129
  library_dep_index: 130
  library_dep_index: 95
}
library_dependencies {
  library_index: 129
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 17
}
library_dependencies {
  library_index: 130
  library_dep_index: 5
  library_dep_index: 12
}
library_dependencies {
  library_index: 131
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 132
}
library_dependencies {
  library_index: 133
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 132
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 1
  dependency_index: 91
  dependency_index: 100
  dependency_index: 104
  dependency_index: 110
  dependency_index: 12
  dependency_index: 119
  dependency_index: 121
  dependency_index: 125
  dependency_index: 30
  dependency_index: 5
  dependency_index: 38
  dependency_index: 131
  dependency_index: 133
  dependency_index: 27
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
