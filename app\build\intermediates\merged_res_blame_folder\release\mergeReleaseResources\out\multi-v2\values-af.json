{"logs": [{"outputFile": "com.Zumbla.Burst2025.app-mergeReleaseResources-54:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3930e81a22611b2ddd91d6283578bd0f\\transformed\\core-1.16.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3418,3516,3618,3716,3814,3921,4030,13936", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3511,3613,3711,3809,3916,4025,4145,14032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bb16bb95ce2c158213264f7a72032a8\\transformed\\material-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1051,1115,1203,1269,1332,1418,1480,1541,1599,1665,1728,1783,1901,1958,2020,2075,2144,2263,2351,2426,2519,2604,2687,2826,2909,2990,3118,3205,3282,3340,3391,3457,3526,3602,3673,3749,3823,3902,3975,4046,4149,4236,4307,4396,4486,4558,4633,4720,4771,4850,4917,4998,5082,5144,5208,5271,5341,5445,5548,5644,5744,5806,5861,5938,6021,6097", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "269,350,430,508,603,691,791,905,986,1046,1110,1198,1264,1327,1413,1475,1536,1594,1660,1723,1778,1896,1953,2015,2070,2139,2258,2346,2421,2514,2599,2682,2821,2904,2985,3113,3200,3277,3335,3386,3452,3521,3597,3668,3744,3818,3897,3970,4041,4144,4231,4302,4391,4481,4553,4628,4715,4766,4845,4912,4993,5077,5139,5203,5266,5336,5440,5543,5639,5739,5801,5856,5933,6016,6092,6165"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,146,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2996,3077,3157,3235,3330,4150,4250,4364,6817,6877,7029,7432,7498,7561,7647,7709,7770,7828,7894,7957,8012,8130,8187,8249,8304,8373,8492,8580,8655,8748,8833,8916,9055,9138,9219,9347,9434,9511,9569,9620,9686,9755,9831,9902,9978,10052,10131,10204,10275,10378,10465,10536,10625,10715,10787,10862,10949,11000,11079,11146,11227,11311,11373,11437,11500,11570,11674,11777,11873,11973,12035,13009,13704,13787,13863", "endLines": "5,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,146,156,157,158", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "319,3072,3152,3230,3325,3413,4245,4359,4440,6872,6936,7112,7493,7556,7642,7704,7765,7823,7889,7952,8007,8125,8182,8244,8299,8368,8487,8575,8650,8743,8828,8911,9050,9133,9214,9342,9429,9506,9564,9615,9681,9750,9826,9897,9973,10047,10126,10199,10270,10373,10460,10531,10620,10710,10782,10857,10944,10995,11074,11141,11222,11306,11368,11432,11495,11565,11669,11772,11868,11968,12030,12085,13081,13782,13858,13931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\66b37f3f60883449bfc7382cd0423a97\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,432,528,634,719,822,940,1017,1093,1184,1277,1372,1466,1565,1658,1753,1852,1947,2041,2122,2229,2334,2431,2539,2642,2744,2898,13623", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "427,523,629,714,817,935,1012,1088,1179,1272,1367,1461,1560,1653,1748,1847,1942,2036,2117,2224,2329,2426,2534,2637,2739,2893,2991,13699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5fb92ec23f9579e12652e527e2b5ff5e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4445,4552,4708,4834,4944,5094,5216,5337,5582,5748,5856,6013,6140,6279,6433,6499,6562", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "4547,4703,4829,4939,5089,5211,5332,5434,5743,5851,6008,6135,6274,6428,6494,6557,6636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0d3df4af221a288d9bcf50e45d897280\\transformed\\preference-1.2.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,345,484,653,732", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "171,259,340,479,648,727,804"}, "to": {"startLines": "66,70,138,147,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6641,6941,12252,13086,14037,14206,14285", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "6707,7024,12328,13220,14201,14280,14357"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\38366cef8399d337aed3da4a7ed40977\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5439", "endColumns": "142", "endOffsets": "5577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a96715ba9df777a8e995ff71e6bff2\\transformed\\jetified-play-services-ads-22.1.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,246,294,349,430,540,590,711,770,904,997,1036,1118,1153,1187,1239,1323,1367", "endColumns": "46,47,54,80,109,49,120,58,133,92,38,81,34,33,51,83,43,55", "endOffsets": "245,293,348,429,539,589,710,769,903,996,1035,1117,1152,1186,1238,1322,1366,1422"}, "to": {"startLines": "135,136,137,139,140,141,142,143,144,145,148,149,150,151,152,153,154,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12090,12141,12193,12333,12418,12532,12586,12711,12774,12912,13225,13268,13354,13393,13431,13487,13575,14362", "endColumns": "50,51,58,84,113,53,124,62,137,96,42,85,38,37,55,87,47,59", "endOffsets": "12136,12188,12247,12413,12527,12581,12706,12769,12907,13004,13263,13349,13388,13426,13482,13570,13618,14417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b9dd55976dd5ae39f237e6587c02b703\\transformed\\browser-1.4.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6712,7117,7219,7332", "endColumns": "104,101,112,99", "endOffsets": "6812,7214,7327,7427"}}]}]}